/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：KnowledgeCacheRepository.java
 * 包    名：com.whiskerguard.ai.repository
 * 描    述：知识缓存仓库接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.KnowledgeCache;
import java.time.Instant;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 知识缓存仓库接口
 *
 * <AUTHOR>
 * @since 1.0
 */
@Repository
public interface KnowledgeCacheRepository extends JpaRepository<KnowledgeCache, Long> {
    /**
     * 根据租户ID、类型和查询关键字查找缓存
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键字
     * @return 缓存对象（如果存在）
     */
    Optional<KnowledgeCache> findByTenantIdAndKnowledgeTypeAndQueryKey(Long tenantId, String knowledgeType, String queryKey);

    /**
     * 删除指定类型且创建时间早于指定时间的缓存
     *
     * @param knowledgeType 知识类型
     * @param createdAt 创建时间
     * @return 删除的记录数量
     */
    int deleteByKnowledgeTypeAndCreatedAtBefore(String knowledgeType, Instant createdAt);

    /**
     * 删除创建时间早于指定时间的所有缓存
     *
     * @param createdAt 创建时间
     * @return 删除的记录数量
     */
    int deleteByCreatedAtBefore(Instant createdAt);
}
