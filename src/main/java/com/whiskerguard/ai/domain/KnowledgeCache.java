/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：KnowledgeCache.java
 * 包    名：com.whiskerguard.ai.domain
 * 描    述：知识缓存实体类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 知识缓存实体类
 *
 * 用于存储从RAG服务检索的知识内容，加速重复查询
 *
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "knowledge_cache")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class KnowledgeCache implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 知识类型
     */
    @NotNull
    @Size(max = 50)
    @Column(name = "knowledge_type", length = 50, nullable = false)
    private String knowledgeType;

    /**
     * 查询关键词
     */
    @NotNull
    @Size(max = 200)
    @Column(name = "query_key", length = 200, nullable = false)
    private String queryKey;

    /**
     * 知识内容
     */
    @NotNull
    @Lob
    @Column(name = "content", nullable = false)
    private String content;

    /**
     * 相似度分数
     */
    @Column(name = "similarity_score")
    private Double similarityScore;

    /**
     * 来源服务
     */
    @Size(max = 50)
    @Column(name = "source_service", length = 50)
    private String sourceService;

    /**
     * 缓存过期时间
     */
    @NotNull
    @Column(name = "expire_time", nullable = false)
    private Instant expireTime;

    /**
     * 访问次数
     */
    @Min(value = 0L)
    @Column(name = "access_count")
    private Long accessCount;

    /**
     * 最后访问时间
     */
    @Column(name = "last_access_time")
    private Instant lastAccessTime;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // 构造函数
    public KnowledgeCache() {}

    // Getter和Setter方法
    public Long getId() {
        return this.id;
    }

    public KnowledgeCache id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public KnowledgeCache tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getKnowledgeType() {
        return this.knowledgeType;
    }

    public KnowledgeCache knowledgeType(String knowledgeType) {
        this.setKnowledgeType(knowledgeType);
        return this;
    }

    public void setKnowledgeType(String knowledgeType) {
        this.knowledgeType = knowledgeType;
    }

    public String getQueryKey() {
        return this.queryKey;
    }

    public KnowledgeCache queryKey(String queryKey) {
        this.setQueryKey(queryKey);
        return this;
    }

    public void setQueryKey(String queryKey) {
        this.queryKey = queryKey;
    }

    public String getContent() {
        return this.content;
    }

    public KnowledgeCache content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Double getSimilarityScore() {
        return this.similarityScore;
    }

    public KnowledgeCache similarityScore(Double similarityScore) {
        this.setSimilarityScore(similarityScore);
        return this;
    }

    public void setSimilarityScore(Double similarityScore) {
        this.similarityScore = similarityScore;
    }

    public String getSourceService() {
        return this.sourceService;
    }

    public KnowledgeCache sourceService(String sourceService) {
        this.setSourceService(sourceService);
        return this;
    }

    public void setSourceService(String sourceService) {
        this.sourceService = sourceService;
    }

    public Instant getExpireTime() {
        return this.expireTime;
    }

    public KnowledgeCache expireTime(Instant expireTime) {
        this.setExpireTime(expireTime);
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public Long getAccessCount() {
        return this.accessCount;
    }

    public KnowledgeCache accessCount(Long accessCount) {
        this.setAccessCount(accessCount);
        return this;
    }

    public void setAccessCount(Long accessCount) {
        this.accessCount = accessCount;
    }

    public Instant getLastAccessTime() {
        return this.lastAccessTime;
    }

    public KnowledgeCache lastAccessTime(Instant lastAccessTime) {
        this.setLastAccessTime(lastAccessTime);
        return this;
    }

    public void setLastAccessTime(Instant lastAccessTime) {
        this.lastAccessTime = lastAccessTime;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public KnowledgeCache metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public KnowledgeCache version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public KnowledgeCache createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public KnowledgeCache createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public KnowledgeCache updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public KnowledgeCache updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public KnowledgeCache isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "KnowledgeCache{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", knowledgeType='" + getKnowledgeType() + "'" +
            ", queryKey='" + getQueryKey() + "'" +
            ", content='" + getContent() + "'" +
            ", similarityScore=" + getSimilarityScore() +
            ", sourceService='" + getSourceService() + "'" +
            ", expireTime='" + getExpireTime() + "'" +
            ", accessCount=" + getAccessCount() +
            ", lastAccessTime='" + getLastAccessTime() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
