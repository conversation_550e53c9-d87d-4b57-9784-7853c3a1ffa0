/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiStreamingService.java
 * 包    名：com.whiskerguard.ai.service.invocation
 * 描    述：AI流式调用服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.ConversationContextService;
import com.whiskerguard.ai.service.ReactiveSensitiveWordFilterService;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.prompt.PromptBuildRequest;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

/**
 * AI 流式调用服务：处理流式调用请求，记录请求和响应
 */
@Service
@Transactional
public class AiStreamingService {

    private final Logger log = LoggerFactory.getLogger(AiStreamingService.class);
    private final AiToolRouter router;
    private final AiRequestRepository requestRepo;
    private final AiToolMetricsService metricsService;
    private final ObjectMapper objectMapper;
    private final ReactiveSensitiveWordFilterService reactiveSensitiveWordFilterService;
    private final PromptBuilderService promptBuilderService;
    private final ConversationContextService conversationContextService;

    public AiStreamingService(
        AiToolRouter router,
        AiRequestRepository requestRepo,
        AiToolMetricsService metricsService,
        ObjectMapper objectMapper,
        ReactiveSensitiveWordFilterService reactiveSensitiveWordFilterService,
        PromptBuilderService promptBuilderService,
        ConversationContextService conversationContextService
    ) {
        this.router = router;
        this.requestRepo = requestRepo;
        this.metricsService = metricsService;
        this.objectMapper = objectMapper;
        this.reactiveSensitiveWordFilterService = reactiveSensitiveWordFilterService;
        this.promptBuilderService = promptBuilderService;
        this.conversationContextService = conversationContextService;
    }

    /**
     * 处理流式调用请求
     * 使用事务确保数据一致性
     * 支持模板化提示词构建
     *
     * @param dto 流式调用请求
     * @return 流式响应
     */
    @Transactional
    public Flux<AiStreamResponseDTO> streamInvoke(AiStreamRequestDTO dto) {
        log.info(
            "开始处理AI流式调用请求，工具类型: {}, 员工ID: {}, 使用模板: {}",
            dto.getToolKey(),
            dto.getEmployeeId(),
            dto.isUseTemplate()
        );

        // 1. 处理模板化提示词（如果启用）
        AiStreamRequestDTO processedDto = processTemplateIfNeeded(dto);

        // 2. 处理对话上下文（如果启用）
        processedDto = processContextIfNeeded(processedDto);

        // 3. 持久化请求
        AiRequest req = new AiRequest();
        req.setToolType(processedDto.getToolKey());
        req.setPrompt(processedDto.getPrompt());
        req.setResponse(""); // 初始化为空字符串，避免数据库约束错误

        // 设置必填的时间字段
        Instant now = Instant.now();
        req.setCreatedAt(now);
        req.setUpdatedAt(now);
        req.setRequestTime(now);

        // 设置必填的状态和版本字段
        req.setStatus(RequestStatus.PROCESSING);
        req.setVersion(1);

        // 设置必填的ID字段
        req.setTenantId(processedDto.getTenantId());
        req.setEmployeeId(processedDto.getEmployeeId()); // 设置员工ID

        // 设置软删除标志
        req.setIsDeleted(false);

        // 设置对话上下文字段
        if (processedDto.getConversationId() != null && !processedDto.getConversationId().trim().isEmpty()) {
            req.setConversationId(processedDto.getConversationId());
            // 获取下一个序号
            Integer nextSequence = conversationContextService.getNextSequence(
                processedDto.getConversationId(),
                processedDto.getTenantId(),
                processedDto.getEmployeeId()
            );
            req.setConversationSequence(nextSequence);
            log.debug("设置对话上下文: conversationId={}, sequence={}", processedDto.getConversationId(), nextSequence);
        }

        try {
            req.setMetadata(processedDto.getMetadata() == null ? null : objectMapper.writeValueAsString(processedDto.getMetadata()));
        } catch (JsonProcessingException e) {
            req.setMetadata(null);
        }

        req = requestRepo.save(req);

        // 4. 创建最终请求对象的引用，用于在流处理完成后更新
        final AiRequest finalReq = req;
        final AtomicReference<StringBuilder> responseBuilder = new AtomicReference<>(new StringBuilder());
        final AiStreamRequestDTO finalProcessedDto = processedDto; // 创建 final 引用

        // 5. 调用路由器的流式方法
        return router
            .routeStream(processedDto)
            .doOnNext(chunk -> {
                // 累积响应内容
                if (chunk.getContent() != null) {
                    responseBuilder.get().append(chunk.getContent());
                }
            })
            .doOnComplete(() -> {
                // 流处理完成后更新请求状态
                finalReq.setStatus(RequestStatus.SUCCESS);
                final Instant responseTime = Instant.now(); // 创建 final 引用
                finalReq.setResponseTime(responseTime);

                // 截断响应内容，确保不超过合理限制
                String content = responseBuilder.get().toString();

                // 使用响应式敏感词过滤服务，避免在reactor线程中使用blocking操作
                reactiveSensitiveWordFilterService
                    .filterContent(content)
                    .subscribe(
                        filteredContent -> {
                            // TEXT 字段可以存储更多内容，设置为 50KB 的限制
                            String finalContent = filteredContent;
                            if (finalContent.length() > 50000) {
                                finalContent = finalContent.substring(0, 49997) + "...";
                                log.warn(
                                    "流式AI响应内容过长，已截断。原长度: {}, 截断后长度: {}",
                                    filteredContent.length(),
                                    finalContent.length()
                                );
                            }
                            finalReq.setResponse(finalContent);

                            requestRepo.save(finalReq);

                            // 保存流式调用指标
                            metricsService.saveStreamCallMetrics(
                                finalProcessedDto.getToolKey(),
                                finalProcessedDto.getTenantId(),
                                finalReq,
                                responseTime
                            );
                        },
                        error -> {
                            log.error("敏感词过滤失败，使用原内容: {}", error.getMessage(), error);
                            // 敏感词过滤失败时，使用原内容继续处理
                            String finalContent = content;
                            if (finalContent.length() > 50000) {
                                finalContent = finalContent.substring(0, 49997) + "...";
                                log.warn("流式AI响应内容过长，已截断。原长度: {}, 截断后长度: {}", content.length(), finalContent.length());
                            }
                            finalReq.setResponse(finalContent);

                            requestRepo.save(finalReq);

                            // 保存流式调用指标
                            metricsService.saveStreamCallMetrics(
                                finalProcessedDto.getToolKey(),
                                finalProcessedDto.getTenantId(),
                                finalReq,
                                responseTime
                            );
                        }
                    );
            })
            .doOnError(error -> {
                // 发生错误时更新请求状态
                finalReq.setStatus(RequestStatus.FAILED);
                finalReq.setResponseTime(Instant.now());

                // 设置响应内容为错误信息或空字符串
                String responseContent = responseBuilder.get().toString();
                if (responseContent.isEmpty()) {
                    responseContent = "流式调用失败";
                }
                // TEXT 字段可以存储更多内容，设置为 50KB 的限制
                if (responseContent.length() > 50000) {
                    responseContent = responseContent.substring(0, 49997) + "...";
                    log.warn(
                        "流式AI错误响应内容过长，已截断。原长度: {}, 截断后长度: {}",
                        responseBuilder.get().toString().length(),
                        responseContent.length()
                    );
                }
                finalReq.setResponse(responseContent);

                // 截断错误信息，设置为 10KB 的限制
                String errorMessage = error.getMessage();
                if (errorMessage != null && errorMessage.length() > 10000) {
                    errorMessage = errorMessage.substring(0, 9997) + "...";
                    log.warn("流式AI错误信息过长，已截断。原长度: {}, 截断后长度: {}", error.getMessage().length(), errorMessage.length());
                }
                finalReq.setErrorMessage(errorMessage);

                requestRepo.save(finalReq);
            });
    }

    /**
     * 处理对话上下文（如果启用）
     * 如果启用了上下文功能，将获取历史对话并构建包含上下文的提示词
     * 如果未启用上下文功能，直接返回原始DTO
     */
    private AiStreamRequestDTO processContextIfNeeded(AiStreamRequestDTO dto) {
        // 检查是否应该启用上下文
        if (!conversationContextService.shouldEnableContext(
            dto.getConversationId(),
            dto.isEnableContext(),
            dto.getMaxContextTurns())) {
            log.debug("未启用上下文功能，使用原始提示词");
            return dto;
        }

        try {
            log.info("启用上下文功能，开始构建上下文提示词，对话ID: {}, 最大轮数: {}",
                dto.getConversationId(), dto.getMaxContextTurns());

            // 获取有效的最大上下文轮数
            int effectiveMaxTurns = conversationContextService.getEffectiveMaxContextTurns(dto.getMaxContextTurns());

            // 获取对话历史
            var history = conversationContextService.getConversationHistory(
                dto.getConversationId(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                effectiveMaxTurns
            );

            log.debug("获取到对话历史记录数: {}", history.size());

            // 构建包含上下文的提示词
            String contextualPrompt = conversationContextService.buildContextualPrompt(dto.getPrompt(), history);

            // 创建新的DTO，使用上下文提示词
            AiStreamRequestDTO processedDto = new AiStreamRequestDTO(
                dto.getToolKey(),
                contextualPrompt, // 使用上下文提示词
                dto.getMetadata(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                dto.getTemplateKey(),
                dto.getTemplateType(),
                dto.getTemplateVariables(),
                dto.isUseTemplate()
            );

            // 保持上下文相关字段
            processedDto.setConversationId(dto.getConversationId());
            processedDto.setEnableContext(dto.isEnableContext());
            processedDto.setMaxContextTurns(dto.getMaxContextTurns());

            log.info(
                "上下文提示词处理完成，原始长度: {}, 上下文长度: {}, 历史记录数: {}",
                dto.getPrompt() != null ? dto.getPrompt().length() : 0,
                contextualPrompt.length(),
                history.size()
            );

            return processedDto;
        } catch (Exception e) {
            log.error("上下文提示词构建失败，将使用原始提示词，错误: {}", e.getMessage(), e);
            // 如果上下文构建失败，降级到原始提示词
            return dto;
        }
    }

    /**
     * 处理模板化提示词（如果启用）
     * 如果启用了模板功能，将使用模板构建最终的提示词
     * 如果未启用模板功能，直接返回原始DTO
     */
    private AiStreamRequestDTO processTemplateIfNeeded(AiStreamRequestDTO dto) {
        if (!dto.isUseTemplate()) {
            log.debug("未启用模板功能，使用原始提示词");
            return dto;
        }

        try {
            log.info("启用模板功能，开始构建模板化提示词，模板键: {}, 模板类型: {}", dto.getTemplateKey(), dto.getTemplateType());

            // 构建模板请求
            PromptBuildRequest request = PromptBuildRequest.builder()
                .tenantId(dto.getTenantId())
                .variables(dto.getTemplateVariables() != null ? dto.getTemplateVariables() : Map.of())
                .enableRagEnhancement(true) // 默认启用RAG增强
                .useCache(true); // 默认使用缓存

            // 优先使用模板键，其次使用模板类型
            if (dto.getTemplateKey() != null && !dto.getTemplateKey().trim().isEmpty()) {
                request.templateKey(dto.getTemplateKey());
                log.debug("使用模板键: {}", dto.getTemplateKey());
            } else if (dto.getTemplateType() != null) {
                request.templateType(dto.getTemplateType());
                log.debug("使用模板类型: {}", dto.getTemplateType());
            } else {
                log.warn("启用了模板功能但未指定模板键或模板类型，将使用原始提示词");
                return dto;
            }

            request = request.build();

            // 构建模板化提示词
            String templatePrompt = promptBuilderService.buildPrompt(request);
            log.debug("模板化提示词构建成功，长度: {}", templatePrompt.length());

            // 创建新的DTO，使用模板化提示词
            AiStreamRequestDTO processedDto = new AiStreamRequestDTO(
                dto.getToolKey(),
                templatePrompt, // 使用模板化提示词
                dto.getMetadata(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                dto.getTemplateKey(),
                dto.getTemplateType(),
                dto.getTemplateVariables(),
                dto.isUseTemplate()
            );

            log.info(
                "模板化提示词处理完成，原始长度: {}, 模板化长度: {}",
                dto.getPrompt() != null ? dto.getPrompt().length() : 0,
                templatePrompt.length()
            );

            return processedDto;
        } catch (Exception e) {
            log.error("模板化提示词构建失败，将使用原始提示词，错误: {}", e.getMessage(), e);
            // 如果模板构建失败，降级到原始提示词
            return dto;
        }
    }
}
