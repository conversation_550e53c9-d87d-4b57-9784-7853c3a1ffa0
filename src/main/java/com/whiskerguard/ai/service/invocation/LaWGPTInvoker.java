/**
 * LaWGPT 调用实现类
 * <p>
 * 负责与 LaWGPT API 进行通信，专门用于法律相关任务。
 */
package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.client.LaWGPTClient;
import com.whiskerguard.ai.domain.AiTool;

import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
public class LaWGPTInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(LaWGPTInvoker.class);
    private final LaWGPTClient laWGPTClient;
    private final RagHelper ragHelper;
    private final BrandIdentityReplacementService brandIdentityReplacementService;

    public LaWGPTInvoker(LaWGPTClient laWGPTClient, RagHelper ragHelper, BrandIdentityReplacementService brandIdentityReplacementService) {
        this.laWGPTClient = laWGPTClient;
        this.ragHelper = ragHelper;
        this.brandIdentityReplacementService = brandIdentityReplacementService;
    }

    @Override
    public boolean supportsStreaming() {
        return false;
    }

    @Override
    public String getToolKey() {
        return "lawgpt";
    }

    @Override
    @Bulkhead(name = "lawgpt", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "lawgpt")
    @Retry(name = "lawgpt")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        try {
            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("原始提示词: {}", originalPrompt);
            log.debug("增强提示词: {}", enhancedPrompt);

            log.debug("LaWGPT请求体: {}", enhancedPrompt);

            long start = System.currentTimeMillis();
            String resp = laWGPTClient.queryLegalModel(enhancedPrompt);
            long duration = System.currentTimeMillis() - start;

            log.debug("LaWGPT响应: {}", resp);

            // LaWGPT 响应解析
            String content = resp;

            // 品牌身份替换处理
            if (content != null && !content.isEmpty()) {
                try {
                    String processedContent = brandIdentityReplacementService.processResponse(content);
                    if (!content.equals(processedContent)) {
                        log.debug("LaWGPT响应已进行品牌身份替换处理");
                        content = processedContent;
                    }
                } catch (Exception e) {
                    log.warn("品牌身份替换处理失败，使用原始内容，错误: {}", e.getMessage());
                }
            }

            // 提取使用量信息
            Map<String, Object> usage = new HashMap<>();
            int estimatedTokens = content.length() / 4; // 粗略估算
            usage.put("total_tokens", estimatedTokens);
            usage.put("estimated_tokens", true);

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            log.error("调用LaWGPT API失败", e);
            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            return AiResult.builder().content("调用LaWGPT API失败: " + e.getMessage()).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        // LaWGPT 当前不支持流式输出，使用默认实现
        return AiToolInvoker.super.invokeStream(dto, cfg);
    }
}
