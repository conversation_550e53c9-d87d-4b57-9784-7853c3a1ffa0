/**
 * ChatLaw 调用实现类
 * <p>
 * 负责与 ChatLaw API 进行通信，专门用于法律相关任务。
 */
package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.client.ChatLawApiClient;
import com.whiskerguard.ai.client.dto.ChatLawResponseDTO;
import com.whiskerguard.ai.domain.AiTool;

import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
public class ChatLawInvoker implements AiToolInvoker {

    private final Logger log = LoggerFactory.getLogger(ChatLawInvoker.class);
    private final ChatLawApiClient chatLawApiClient;
    private final RagHelper ragHelper;


    public ChatLawInvoker(ChatLawApiClient chatLawApiClient, RagHelper ragHelper) {
        this.chatLawApiClient = chatLawApiClient;
        this.ragHelper = ragHelper;
    }

    @Override
    public boolean supportsStreaming() {
        return false;
    }

    @Override
    public String getToolKey() {
        return "chatlaw";
    }

    @Override
    @Bulkhead(name = "chatlaw", type = Bulkhead.Type.SEMAPHORE)
    @CircuitBreaker(name = "chatlaw")
    @Retry(name = "chatlaw")
    public AiResult invoke(AiInvocationRequestDTO dto, AiTool cfg) {
        try {
            // 使用 RAG 增强提示词
            String originalPrompt = dto.getPrompt();
            String enhancedPrompt = ragHelper.enhancePromptWithRag(dto);
            log.debug("原始提示词: {}", originalPrompt);
            log.debug("增强提示词: {}", enhancedPrompt);

            log.debug("ChatLaw请求体: {}", enhancedPrompt);

            long start = System.currentTimeMillis();
            ChatLawResponseDTO resp = chatLawApiClient.queryLegalModel(enhancedPrompt);
            long duration = System.currentTimeMillis() - start;

            log.debug("ChatLaw响应: {}", resp);

            // ChatLaw 响应解析
            String content = resp.getChoices().get(0).getMessage().getContent();



            // 提取使用量信息
            Map<String, Object> usage = new HashMap<>();
            if (resp.getUsage() != null) {
                usage.put("prompt_tokens", resp.getUsage().getPromptTokens());
                usage.put("completion_tokens", resp.getUsage().getCompletionTokens());
                usage.put("total_tokens", resp.getUsage().getTotalTokens());
            }

            return AiResult.builder().content(content).usage(usage).durationMs(duration).build();
        } catch (Exception e) {
            log.error("调用ChatLaw API失败", e);
            // 返回一个包含错误信息的结果
            Map<String, Object> errorUsage = new HashMap<>();
            errorUsage.put("error", e.getMessage());
            return AiResult.builder().content("调用ChatLaw API失败: " + e.getMessage()).usage(errorUsage).durationMs(0).build();
        }
    }

    @Override
    public Flux<AiStreamResponseDTO> invokeStream(AiStreamRequestDTO dto, AiTool cfg) {
        // ChatLaw 当前不支持流式输出，使用默认实现
        return AiToolInvoker.super.invokeStream(dto, cfg);
    }
}
