package com.whiskerguard.ai.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 系统角色配置属性类
 * <p>
 * 用于配置AI系统的统一角色身份，包括身份名称、描述、问候语等信息。
 * 这些配置将在提示词模板中使用，确保所有AI回复都具有一致的品牌身份。
 *
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "whiskerguard.ai.system.role")
public class SystemRoleProperties {

    /**
     * AI身份名称
     * 例如："猫伯伯智能合规管家"
     */
    private String identity = "猫伯伯智能合规管家";

    /**
     * 角色描述
     * 例如："专业的AI助手，专门为用户提供合规、法律、合同审查等服务"
     */
    private String description = "专业的AI助手，专门为用户提供合规、法律、合同审查等服务";

    /**
     * 问候语
     * 例如："你好！我是猫伯伯智能合规管家"
     */
    private String greeting = "你好！我是猫伯伯智能合规管家";

    /**
     * 能力描述
     * 例如："我可以帮助您进行合同审查、法律咨询、合规分析等专业服务"
     */
    private String capabilities = "我可以帮助您进行合同审查、法律咨询、合规分析等专业服务";

    /**
     * 个性特征
     * 例如："请随时告诉我您的需求，我会尽力为您提供专业的建议和帮助"
     */
    private String personality = "请随时告诉我您的需求，我会尽力为您提供专业的建议和帮助";

    /**
     * 是否启用系统角色定义功能
     */
    private boolean enabled = true;

    /**
     * 构建完整的系统角色定义提示词
     *
     * @return 系统角色定义的提示词文本
     */
    public String buildSystemRolePrompt() {
        if (!enabled) {
            return "";
        }

        StringBuilder rolePrompt = new StringBuilder();
        rolePrompt.append("# 系统角色定义\n");
        rolePrompt.append("你是").append(identity).append("，").append(description).append("。\n");
        rolePrompt.append("当用户询问你的身份时，请始终回答"我是").append(identity).append(""。\n");
        rolePrompt.append("请保持专业、友好的语调，专注于为用户提供准确的法律和合规建议。\n");

        if (capabilities != null && !capabilities.trim().isEmpty()) {
            rolePrompt.append("你的主要能力包括：").append(capabilities).append("。\n");
        }

        if (personality != null && !personality.trim().isEmpty()) {
            rolePrompt.append(personality).append("\n");
        }

        rolePrompt.append("\n");
        return rolePrompt.toString();
    }

    /**
     * 构建简化的角色身份声明
     * 用于在提示词中简单声明身份
     *
     * @return 简化的身份声明
     */
    public String buildSimpleIdentity() {
        if (!enabled) {
            return "";
        }
        return "我是" + identity + "，" + description + "。\n";
    }

    // Getter and Setter methods

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getGreeting() {
        return greeting;
    }

    public void setGreeting(String greeting) {
        this.greeting = greeting;
    }

    public String getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(String capabilities) {
        this.capabilities = capabilities;
    }

    public String getPersonality() {
        return personality;
    }

    public void setPersonality(String personality) {
        this.personality = personality;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return "SystemRoleProperties{" +
            "identity='" + identity + '\'' +
            ", description='" + description + '\'' +
            ", greeting='" + greeting + '\'' +
            ", capabilities='" + capabilities + '\'' +
            ", personality='" + personality + '\'' +
            ", enabled=" + enabled +
            '}';
    }
}
