# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

---
# Conditionally disable springdoc on missing api-docs profile
spring:
  config:
    activate:
      on-profile: '!api-docs'
springdoc:
  api-docs:
    enabled: false
---
feign:
  circuitbreaker:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000 # 连接超时 3 秒
        readTimeout: 15000 # 读取超时 15 秒，为 AI 调用预留足够时间
      whiskerguardgeneralservice:
        connectTimeout: 2000 # 敏感词服务连接超时 2 秒
        readTimeout: 5000 # 敏感词服务读取超时 5 秒
      whiskerguard-rag-service:
        connectTimeout: 1000 # RAG 服务连接超时 1 秒
        readTimeout: 3000 # RAG 服务读取超时 3 秒

# Resilience4j 配置 - 优化 AI 服务性能和稳定性
resilience4j:
  circuitbreaker:
    instances:
      rag-service:
        slidingWindowSize: 10
        minimumNumberOfCalls: 3 # 降低最小调用数，快速熔断
        failureRateThreshold: 30 # 降低失败率阈值，更快熔断
        waitDurationInOpenState: 15s # 降低熔断器开启时间
        permittedNumberOfCallsInHalfOpenState: 3
        slowCallRateThreshold: 50
        slowCallDurationThreshold: 2s # 降低慢调用阈值
      doubao:
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        failureRateThreshold: 60
        waitDurationInOpenState: 60s
        slowCallDurationThreshold: 20s
      kimi:
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        failureRateThreshold: 60
        waitDurationInOpenState: 60s
        slowCallDurationThreshold: 20s
      claude:
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        failureRateThreshold: 60
        waitDurationInOpenState: 60s
        slowCallDurationThreshold: 20s
      deepseek:
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        failureRateThreshold: 60
        waitDurationInOpenState: 60s
        slowCallDurationThreshold: 20s
      chatlaw:
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        failureRateThreshold: 60
        waitDurationInOpenState: 60s
        slowCallDurationThreshold: 20s
      lawgpt:
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        failureRateThreshold: 60
        waitDurationInOpenState: 60s
        slowCallDurationThreshold: 20s
  retry:
    instances:
      rag-service:
        maxAttempts: 2
        waitDuration: 1s
        exponentialBackoffMultiplier: 1.5
      doubao:
        maxAttempts: 2
        waitDuration: 2s
        exponentialBackoffMultiplier: 2.0
      kimi:
        maxAttempts: 2
        waitDuration: 2s
        exponentialBackoffMultiplier: 2.0
      claude:
        maxAttempts: 2
        waitDuration: 2s
        exponentialBackoffMultiplier: 2.0
      deepseek:
        maxAttempts: 2
        waitDuration: 2s
        exponentialBackoffMultiplier: 2.0
      chatlaw:
        maxAttempts: 2
        waitDuration: 2s
        exponentialBackoffMultiplier: 2.0
      lawgpt:
        maxAttempts: 2
        waitDuration: 2s
        exponentialBackoffMultiplier: 2.0
  bulkhead:
    instances:
      doubao:
        maxConcurrentCalls: 10
        maxWaitDuration: 5s
      kimi:
        maxConcurrentCalls: 10
        maxWaitDuration: 5s
      claude:
        maxConcurrentCalls: 10
        maxWaitDuration: 5s
      deepseek:
        maxConcurrentCalls: 10
        maxWaitDuration: 5s
      chatlaw:
        maxConcurrentCalls: 10
        maxWaitDuration: 5s
      lawgpt:
        maxConcurrentCalls: 5
        maxWaitDuration: 5s
      doubao-stream:
        maxConcurrentCalls: 5
        maxWaitDuration: 3s
      kimi-stream:
        maxConcurrentCalls: 5
        maxWaitDuration: 3s
      claude-stream:
        maxConcurrentCalls: 5
        maxWaitDuration: 3s
  timeout:
    instances:
      ai-calls:
        timeout-duration: 10s # AI调用总超时时间

management:
  endpoints:
    web:
      base-path: /management
      exposure:
        include:
          - configprops
          - env
          - health
          - info
          - jhimetrics
          - jhiopenapigroups
          - logfile
          - loggers
          - prometheus
          - threaddump
          - caches
          - liquibase
  endpoint:
    health:
      show-details: when-authorized
      roles: 'ROLE_ADMIN'
      probes:
        enabled: true
      group:
        liveness:
          include: livenessState
        readiness:
          include: readinessState,db
    jhimetrics:
      enabled: true
  info:
    git:
      mode: full
    env:
      enabled: true
  health:
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
  prometheus:
    metrics:
      export:
        enabled: true
        step: 60
  observations:
    key-values:
      application: ${spring.application.name}
  metrics:
    enable:
      http: true
      jvm: true
      logback: true
      process: true
      system: true
    distribution:
      percentiles-histogram:
        all: true
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    data:
      repository:
        autotime:
          enabled: true
    tags:
      application: ${spring.application.name}

spring:
  application:
    name: whiskerguardAiService
  cloud:
    consul:
      discovery:
        healthCheckPath: /management/health
        instanceId: whiskerguardaiservice:${spring.application.instance-id:${random.value}}
        service-name: whiskerguardaiservice
      config:
        watch:
          enabled: false
  docker:
    compose:
      enabled: false
      lifecycle-management: start-only
      file: src/main/docker/mysql.yml
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: '@spring.profiles.active@'
    group:
      dev:
        - dev
        - api-docs
        # Uncomment to activate TLS for the dev profile
        #- tls
  jmx:
    enabled: false
  data:
    jpa:
      repositories:
        bootstrap-mode: deferred
  jpa:
    open-in-view: false
    properties:
      hibernate.jdbc.time_zone: Asia/Shanghai
      hibernate.timezone.default_storage: NORMALIZE
      hibernate.type.preferred_instant_jdbc_type: TIMESTAMP
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: true
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      # modify batch size as necessary
      hibernate.jdbc.batch_size: 25
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
  mvc:
    problemdetails:
      enabled: true
  security:
    oauth2:
      resourceserver:
        jwt:
          authority-prefix: ''
          authorities-claim-name: auth
  task:
    execution:
      thread-name-prefix: whiskerguard-ai-service-task-
      pool:
        core-size: 8 # 增加核心线程数
        max-size: 32 # 增加最大线程数
        queue-capacity: 500 # 队列容量
        keep-alive: 30s # 线程空闲时间
        allow-core-thread-timeout: true # 允许核心线程超时
    scheduling:
      thread-name-prefix: whiskerguard-ai-service-scheduling-
      pool:
        size: 2
  thymeleaf:
    mode: HTML
  output:
    ansi:
      console-available: true

server:
  servlet:
    session:
      cookie:
        http-only: true

springdoc:
  show-actuator: true

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'whiskerguardAiServiceApp'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #   allowed-origins: "http://localhost:8100,http://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params"
  #   allow-credentials: true
  #   max-age: 1800
  mail:
    from: whiskerguardAiService@localhost
  api-docs:
    default-include-pattern: /api/**
    management-include-pattern: /management/**
    title: Whiskerguard Ai Service API
    description: Whiskerguard Ai Service API documentation
    version: 0.0.1
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license: unlicensed
    license-url:
  security:

# jhipster-needle-add-application-yaml-document
---
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:

whiskerguard:
  ai:
    # 系统角色定义配置
    system:
      role:
        identity: '猫伯伯智能合规管家' # 统一的AI身份名称
        description: '专业的AI助手，专门为用户提供合规、法律、合同审查等服务' # 角色描述
        greeting: '您好！我是猫伯伯智能合规管家' # 问候语
        capabilities: '我可以帮助您进行合同审查、法律咨询、合规分析等专业服务' # 能力描述
        personality: '请随时告诉我您的需求，我会尽力为您提供专业的建议和帮助' # 个性特征
        enabled: true # 启用系统角色定义功能

    # 缓存总开关
    cache:
      enabled: true # 启用AI响应缓存
      max-size: 1000 # 最大缓存条目数
      expire-after-write: 30m # 写入后过期时间
      expire-after-access: 10m # 访问后过期时间
      bypass-workflow: true # 缓存命中时绕过Temporal工作流

    # RAG缓存优化配置
    rag:
      enabled: true # 启用RAG功能
      timeout: 2000 # RAG调用超时时间(ms) - 从8秒降到2秒
      cache-enabled: true # 启用RAG结果缓存
      cache-expire: 2h # RAG缓存过期时间
      top-k: 3 # 检索结果数量（降低以提升性能）
      min-score: 0.7 # 最小相似度阈值（提高以过滤低质量结果）
      fast-fail: true # 快速失败模式

    # 内部制度审查配置
    policy-review:
      enabled: true # 启用内部制度审查功能
      max-content-length: 1000000 # 制度内容最大长度（字符）
      min-content-length: 10 # 制度内容最小长度（字符）
      default-ai-model: 'deepseek' # 默认使用的AI模型
      analysis-timeout: 30000 # 分析超时时间（毫秒）
      max-risk-points: 50 # 最大风险点数量
      max-clause-issues: 30 # 最大条款问题数量
      max-related-parties: 20 # 最大关联方数量

      # 风险评估配置
      risk-assessment:
        enable-company-background: true # 启用企业背景调查
        enable-legal-compliance: true # 启用法律合规检查
        enable-related-party-check: true # 启用关联方检查
        default-risk-tolerance: 'MEDIUM' # 默认风险容忍度

      # 缓存配置
      cache:
        enabled: true # 启用制度审查结果缓存
        expire-time: 1h # 缓存过期时间
        max-entries: 500 # 最大缓存条目数

    # ChatLaw API configuration (via 302.ai)
    # ChatLaw API配置（通过302.ai）
    chatlaw:
      api:
        url: https://api.302.ai/v1/chat/completions
        key: ${CHATLAW_API_KEY:} # Set via environment variable for security
      model: ChatLaw
      max-tokens: 2000
      temperature: 0.7

    # LaWGPT private deployment configuration
    # LaWGPT私有部署配置
    lawgpt:
      enabled: false # Set to true when LaWGPT is deployed
      api:
        url: http://localhost:7860/api/predict # Update with actual deployment URL
      max-length: 2000
      temperature: 0.7
      top-p: 0.9

# ===================================================================
# 数据库连接池优化配置
# ===================================================================
spring:
  datasource:
    hikari:
      maximum-pool-size: 20 # 增加连接池大小
      minimum-idle: 5
      connection-timeout: 3000 # 连接超时3秒
