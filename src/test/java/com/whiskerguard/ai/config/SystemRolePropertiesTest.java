package com.whiskerguard.ai.config;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * 系统角色配置属性测试类
 * <p>
 * 测试SystemRoleProperties的各种配置和方法功能
 */
class SystemRolePropertiesTest {

    private SystemRoleProperties systemRoleProperties;

    @BeforeEach
    void setUp() {
        systemRoleProperties = new SystemRoleProperties();
        // 设置测试配置
        systemRoleProperties.setIdentity("猫伯伯智能合规管家");
        systemRoleProperties.setDescription("专业的AI助手，专门为用户提供合规、法律、合同审查等服务");
        systemRoleProperties.setGreeting("你好！我是猫伯伯智能合规管家");
        systemRoleProperties.setCapabilities("我可以帮助您进行合同审查、法律咨询、合规分析等专业服务");
        systemRoleProperties.setPersonality("请随时告诉我您的需求，我会尽力为您提供专业的建议和帮助");
        systemRoleProperties.setEnabled(true);
    }

    @Test
    void testBuildSystemRolePrompt() {
        // 测试构建完整的系统角色定义提示词
        String systemRolePrompt = systemRoleProperties.buildSystemRolePrompt();
        
        assertThat(systemRolePrompt).isNotNull();
        assertThat(systemRolePrompt).isNotEmpty();
        assertThat(systemRolePrompt).contains("# 系统角色定义");
        assertThat(systemRolePrompt).contains("你是猫伯伯智能合规管家");
        assertThat(systemRoleProperties.getDescription()).isNotNull();
        assertThat(systemRolePrompt).contains(systemRoleProperties.getDescription());
        assertThat(systemRolePrompt).contains("当用户询问你的身份时，请始终回答\"我是猫伯伯智能合规管家\"");
        assertThat(systemRolePrompt).contains("请保持专业、友好的语调");
        assertThat(systemRolePrompt).contains(systemRoleProperties.getCapabilities());
        assertThat(systemRolePrompt).contains(systemRoleProperties.getPersonality());
    }

    @Test
    void testBuildSimpleIdentity() {
        // 测试构建简化的角色身份声明
        String simpleIdentity = systemRoleProperties.buildSimpleIdentity();
        
        assertThat(simpleIdentity).isNotNull();
        assertThat(simpleIdentity).isNotEmpty();
        assertThat(simpleIdentity).contains("我是猫伯伯智能合规管家");
        assertThat(simpleIdentity).contains(systemRoleProperties.getDescription());
    }

    @Test
    void testDisabledSystemRole() {
        // 测试禁用系统角色定义功能
        systemRoleProperties.setEnabled(false);
        
        String systemRolePrompt = systemRoleProperties.buildSystemRolePrompt();
        String simpleIdentity = systemRoleProperties.buildSimpleIdentity();
        
        assertThat(systemRolePrompt).isEmpty();
        assertThat(simpleIdentity).isEmpty();
    }

    @Test
    void testEmptyCapabilities() {
        // 测试空能力描述
        systemRoleProperties.setCapabilities("");
        
        String systemRolePrompt = systemRoleProperties.buildSystemRolePrompt();
        
        assertThat(systemRolePrompt).isNotNull();
        assertThat(systemRolePrompt).contains("# 系统角色定义");
        assertThat(systemRolePrompt).contains("你是猫伯伯智能合规管家");
        // 空能力描述不应该出现在提示词中
        assertThat(systemRolePrompt).doesNotContain("你的主要能力包括：。");
    }

    @Test
    void testEmptyPersonality() {
        // 测试空个性特征
        systemRoleProperties.setPersonality(null);
        
        String systemRolePrompt = systemRoleProperties.buildSystemRolePrompt();
        
        assertThat(systemRolePrompt).isNotNull();
        assertThat(systemRolePrompt).contains("# 系统角色定义");
        assertThat(systemRolePrompt).contains("你是猫伯伯智能合规管家");
        // null个性特征不应该出现在提示词中
        assertThat(systemRolePrompt).doesNotContain("null");
    }

    @Test
    void testDefaultValues() {
        // 测试默认值
        SystemRoleProperties defaultProperties = new SystemRoleProperties();
        
        assertThat(defaultProperties.getIdentity()).isEqualTo("猫伯伯智能合规管家");
        assertThat(defaultProperties.getDescription()).isEqualTo("专业的AI助手，专门为用户提供合规、法律、合同审查等服务");
        assertThat(defaultProperties.getGreeting()).isEqualTo("你好！我是猫伯伯智能合规管家");
        assertThat(defaultProperties.getCapabilities()).isEqualTo("我可以帮助您进行合同审查、法律咨询、合规分析等专业服务");
        assertThat(defaultProperties.getPersonality()).isEqualTo("请随时告诉我您的需求，我会尽力为您提供专业的建议和帮助");
        assertThat(defaultProperties.isEnabled()).isTrue();
    }

    @Test
    void testToString() {
        // 测试toString方法
        String toString = systemRoleProperties.toString();
        
        assertThat(toString).isNotNull();
        assertThat(toString).contains("SystemRoleProperties");
        assertThat(toString).contains("identity='猫伯伯智能合规管家'");
        assertThat(toString).contains("enabled=true");
    }

    @Test
    void testCustomIdentity() {
        // 测试自定义身份
        systemRoleProperties.setIdentity("测试AI助手");
        systemRoleProperties.setDescription("测试用的AI助手");
        
        String systemRolePrompt = systemRoleProperties.buildSystemRolePrompt();
        String simpleIdentity = systemRoleProperties.buildSimpleIdentity();
        
        assertThat(systemRolePrompt).contains("你是测试AI助手");
        assertThat(systemRolePrompt).contains("当用户询问你的身份时，请始终回答"我是测试AI助手"");
        assertThat(simpleIdentity).contains("我是测试AI助手");
        assertThat(simpleIdentity).contains("测试用的AI助手");
    }
}
