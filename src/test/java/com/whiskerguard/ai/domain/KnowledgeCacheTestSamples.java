package com.whiskerguard.ai.domain;

import java.time.Instant;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class KnowledgeCacheTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static KnowledgeCache getKnowledgeCacheSample1() {
        return new KnowledgeCache()
            .id(1L)
            .tenantId(1L)
            .knowledgeType("knowledgeType1")
            .queryKey("queryKey1")
            .content("content1")
            .similarityScore(1.0)
            .sourceService("sourceService1")
            .expireTime(Instant.ofEpochMilli(0L))
            .accessCount(1L)
            .lastAccessTime(Instant.ofEpochMilli(0L))
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .createdAt(Instant.ofEpochMilli(0L))
            .updatedBy("updatedBy1")
            .updatedAt(Instant.ofEpochMilli(0L))
            .isDeleted(false);
    }

    public static KnowledgeCache getKnowledgeCacheSample2() {
        return new KnowledgeCache()
            .id(2L)
            .tenantId(2L)
            .knowledgeType("knowledgeType2")
            .queryKey("queryKey2")
            .content("content2")
            .similarityScore(2.0)
            .sourceService("sourceService2")
            .expireTime(Instant.ofEpochMilli(1000L))
            .accessCount(2L)
            .lastAccessTime(Instant.ofEpochMilli(1000L))
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .createdAt(Instant.ofEpochMilli(1000L))
            .updatedBy("updatedBy2")
            .updatedAt(Instant.ofEpochMilli(1000L))
            .isDeleted(true);
    }

    public static KnowledgeCache getKnowledgeCacheRandomSampleGenerator() {
        return new KnowledgeCache()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .knowledgeType(UUID.randomUUID().toString())
            .queryKey(UUID.randomUUID().toString())
            .content(UUID.randomUUID().toString())
            .similarityScore(random.nextDouble())
            .sourceService(UUID.randomUUID().toString())
            .expireTime(Instant.now())
            .accessCount(longCount.incrementAndGet())
            .lastAccessTime(Instant.now())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .createdAt(Instant.now())
            .updatedBy(UUID.randomUUID().toString())
            .updatedAt(Instant.now())
            .isDeleted(random.nextBoolean());
    }
}
