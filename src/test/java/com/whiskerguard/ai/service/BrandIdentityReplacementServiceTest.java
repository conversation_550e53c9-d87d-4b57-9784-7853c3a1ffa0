package com.whiskerguard.ai.service;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * 品牌身份替换服务测试类
 * <p>
 * 测试各种AI模型身份替换场景，确保品牌统一性
 */
class BrandIdentityReplacementServiceTest {

    private BrandIdentityReplacementService brandIdentityReplacementService;

    @BeforeEach
    void setUp() {
        brandIdentityReplacementService = new BrandIdentityReplacementService();
        // 设置测试配置
        ReflectionTestUtils.setField(brandIdentityReplacementService, "brandIdentity", "我是猫伯伯智能合规管家");
        ReflectionTestUtils.setField(brandIdentityReplacementService, "replacementEnabled", true);
    }

    @Test
    void testKimiIdentityReplacement() {
        // 测试Kimi身份替换
        String input = "我是Kimi，一个由月之暗面科技开发的AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是Kimi");
    }

    @Test
    void testDoubaoIdentityReplacement() {
        // 测试豆包身份替换
        String input = "我是豆包，字节跳动开发的AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是豆包");
    }

    @Test
    void testDeepSeekIdentityReplacement() {
        // 测试DeepSeek身份替换
        String input = "我是DeepSeek，一个专业的AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是DeepSeek");
    }

    @Test
    void testClaudeIdentityReplacement() {
        // 测试Claude身份替换
        String input = "我是Claude，由Anthropic开发的AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是Claude");
    }

    @Test
    void testChatGPTIdentityReplacement() {
        // 测试ChatGPT身份替换
        String input = "我是ChatGPT，OpenAI开发的语言模型。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是ChatGPT");
    }

    @Test
    void testChatLawIdentityReplacement() {
        // 测试ChatLaw身份替换
        String input = "我是ChatLaw，专业的法律AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是ChatLaw");
    }

    @Test
    void testLaWGPTIdentityReplacement() {
        // 测试LaWGPT身份替换
        String input = "我是LaWGPT，专门处理法律问题的AI模型。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是LaWGPT");
    }

    @Test
    void testLegalAIAssistantReplacement() {
        // 测试法律AI助手身份替换
        String input = "我是一个法律AI助手，专门为您提供法律咨询服务。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是一个法律AI助手");
    }

    @Test
    void testGenericAIAssistantReplacement() {
        // 测试通用AI助手身份替换
        String input = "我是一个AI助手，可以帮助您解决问题。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是一个AI助手");
    }

    @Test
    void testMultipleIdentitiesInSameText() {
        // 测试同一文本中的多个身份替换
        String input = "我是Kimi，也可以说我是豆包，或者我是ChatLaw，总之我是一个AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是Kimi");
        assertThat(result).doesNotContain("我是豆包");
        assertThat(result).doesNotContain("我是ChatLaw");
        assertThat(result).doesNotContain("我是一个AI助手");
    }

    @Test
    void testNoReplacementNeeded() {
        // 测试不需要替换的情况
        String input = "这是一个关于合同审查的专业分析报告。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).isEqualTo(input);
    }

    @Test
    void testEmptyInput() {
        // 测试空输入
        String result = brandIdentityReplacementService.processResponse("");
        assertThat(result).isEmpty();
    }

    @Test
    void testNullInput() {
        // 测试null输入
        String result = brandIdentityReplacementService.processResponse(null);
        assertThat(result).isNull();
    }

    @Test
    void testReplacementDisabled() {
        // 测试禁用替换功能
        ReflectionTestUtils.setField(brandIdentityReplacementService, "replacementEnabled", false);
        String input = "我是Kimi，一个AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).isEqualTo(input);
    }

    @Test
    void testCaseInsensitiveReplacement() {
        // 测试大小写不敏感的替换
        String input = "我是kimi，一个ai助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是kimi");
    }

    @Test
    void testComplexSentenceReplacement() {
        // 测试复杂句子中的身份替换
        String input = "您好！我是Kimi，很高兴为您服务。我可以帮助您进行合同审查和法律咨询。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是Kimi");
        assertThat(result).contains("很高兴为您服务");
        assertThat(result).contains("合同审查和法律咨询");
    }

    @Test
    void testPunctuationHandling() {
        // 测试标点符号处理
        String input = "我是Kimi，专业的AI助手！";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("我是Kimi");
        assertThat(result).contains("专业的");
    }

    @Test
    void testWhitespaceHandling() {
        // 测试空白字符处理
        String input = "我是  Kimi  ，一个AI助手。";
        String result = brandIdentityReplacementService.processResponse(input);
        assertThat(result).contains("我是猫伯伯智能合规管家");
        assertThat(result).doesNotContain("Kimi");
    }

    @Test
    void testGetBrandIdentity() {
        // 测试获取品牌身份
        String brandIdentity = brandIdentityReplacementService.getBrandIdentity();
        assertThat(brandIdentity).isEqualTo("我是猫伯伯智能合规管家");
    }

    @Test
    void testIsReplacementEnabled() {
        // 测试检查是否启用替换功能
        boolean enabled = brandIdentityReplacementService.isReplacementEnabled();
        assertThat(enabled).isTrue();
    }
}
